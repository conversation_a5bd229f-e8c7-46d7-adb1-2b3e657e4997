import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from config import settings

logger = logging.getLogger(__name__)

class CloudWatchLogger:
    """
    Print Logger for structured logging with before/after object states
    and search by ID functionality for debugging purposes.
    """
    
    def __init__(self, region: str = None):
        self.region = region or settings.AWS_REGION
        self._log_entries = []  # Store logs in memory for search functionality
    
    def _sanitize_data(self, data: Any) -> Any:
        """Sanitize sensitive data before logging"""
        if isinstance(data, dict):
            sanitized = {}
            sensitive_fields = ['password', 'token', 'secret', 'key', 'auth0Id', 'cognitoId']
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in sensitive_fields):
                    sanitized[key] = "***REDACTED***"
                else:
                    sanitized[key] = self._sanitize_data(value)
            return sanitized
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        else:
            return data
    
    def log_api_operation(
        self,
        operation: str,
        resource_id: str,
        resource_type: str,
        before_state: Optional[Dict[str, Any]] = None,
        after_state: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ):
        """
        Log API operation with before/after states for debugging
        
        Args:
            operation: The operation being performed (e.g., 'update', 'create', 'delete')
            resource_id: The ID of the resource being operated on (for search functionality)
            resource_type: The type of resource (e.g., 'member', 'organization', 'admin')
            before_state: The state before the operation
            after_state: The state after the operation
            user_id: The ID of the user performing the operation
            additional_context: Any additional context for the operation
            error: Error message if operation failed
        """
        try:
            # Create log entry
            log_entry = {
                "timestamp": datetime.utcnow().isoformat(),
                "operation": operation,
                "resource_id": resource_id,
                "resource_type": resource_type,
                "user_id": user_id,
                "before_state": self._sanitize_data(before_state) if before_state else None,
                "after_state": self._sanitize_data(after_state) if after_state else None,
                "additional_context": additional_context or {},
                "error": error,
                "environment": "external"  # or "internal" based on deployment
            }
            
            # Store log entry in memory for search functionality
            self._log_entries.append({
                'timestamp': int(datetime.utcnow().timestamp() * 1000),
                'logStreamName': f"{resource_type}-{operation}-{datetime.utcnow().strftime('%Y-%m-%d')}",
                'data': log_entry
            })
            
            # Print the log entry
            if before_state:
                print(f"BEFORE STATE - {operation.upper()} on {resource_type.upper()} ID: {resource_id} - {json.dumps(self._sanitize_data(before_state), default=str)}")
            
            if after_state:
                print(f"AFTER STATE - {operation.upper()} on {resource_type.upper()} ID: {resource_id} - {json.dumps(self._sanitize_data(after_state), default=str)}")
            
            if error:
                print(f"ERROR - {operation.upper()} on {resource_type.upper()} ID: {resource_id} - {error}")
            
            logger.info(f"Print log entry created for {operation} on {resource_type} {resource_id}")
            
        except Exception as e:
            logger.error(f"Failed to create print log: {str(e)}")
            # Fallback to standard logging
            logger.info(f"API Operation: {operation} | Resource: {resource_type} | ID: {resource_id} | User: {user_id}")
            if before_state:
                logger.info(f"Before State: {json.dumps(self._sanitize_data(before_state), default=str)}")
            if after_state:
                logger.info(f"After State: {json.dumps(self._sanitize_data(after_state), default=str)}")
            if error:
                logger.error(f"Error: {error}")
    
    def search_logs_by_id(
        self,
        resource_id: str,
        resource_type: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> list:
        """
        Search in-memory logs by resource ID for debugging purposes
        
        Args:
            resource_id: The resource ID to search for
            resource_type: Optional resource type filter
            start_time: Start time for search (default: 24 hours ago)
            end_time: End time for search (default: now)
            limit: Maximum number of log entries to return
            
        Returns:
            List of log entries matching the criteria
        """
        try:
            # Set default time range if not provided
            if not end_time:
                end_time = datetime.utcnow()
            if not start_time:
                start_time = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Filter log entries from memory
            filtered_entries = []
            for entry in self._log_entries:
                # Check timestamp range
                entry_time = datetime.fromtimestamp(entry['timestamp'] / 1000)
                if entry_time < start_time or entry_time > end_time:
                    continue
                
                # Check resource ID
                if entry['data'].get('resource_id') != resource_id:
                    continue
                
                # Check resource type if specified
                if resource_type and entry['data'].get('resource_type') != resource_type:
                    continue
                
                filtered_entries.append(entry)
            
            # Apply limit
            filtered_entries = filtered_entries[-limit:] if len(filtered_entries) > limit else filtered_entries
            
            logger.info(f"Found {len(filtered_entries)} log entries for resource ID: {resource_id}")
            return filtered_entries
            
        except Exception as e:
            logger.error(f"Failed to search in-memory logs: {str(e)}")
            return []

# Global instance for easy access
cloudwatch_logger = CloudWatchLogger() 